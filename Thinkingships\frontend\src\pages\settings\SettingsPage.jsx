import { useState } from 'react';
import { FaShieldAlt } from 'react-icons/fa';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
    delete: false
  });

  const togglePasswordVisibility = (field) => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-10">
        {/* Settings Header */}
        <div className="mb-10">
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            Account Settings
          </h1>
          <p className="text-gray-600 text-lg">
            Manage your account preferences, security settings, and personal information
          </p>
        </div>

        {/* Settings Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {/* Account Card */}
          <div
            onClick={() => setActiveTab('account')}
            className={`group relative p-6 rounded-xl cursor-pointer transition-all duration-200 ${
              activeTab === 'account'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 transition-all duration-200 ${
                activeTab === 'account'
                  ? 'bg-white/20 text-white'
                  : 'bg-blue-50 text-blue-600 group-hover:bg-blue-100'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Account</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'account' ? 'text-blue-100' : 'text-gray-600'}`}>
                Manage your account details, change your email or password and delete your account if needed.
              </p>
            </div>
          </div>

          {/* Notifications Card */}
          <div
            onClick={() => setActiveTab('notifications')}
            className={`group relative p-6 rounded-xl cursor-pointer transition-all duration-200 ${
              activeTab === 'notifications'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 transition-all duration-200 ${
                activeTab === 'notifications'
                  ? 'bg-white/20 text-white'
                  : 'bg-amber-50 text-amber-600 group-hover:bg-amber-100'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Notifications</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'notifications' ? 'text-blue-100' : 'text-gray-600'}`}>
                Customize your notification preferences for messages, content engagement and other updates.
              </p>
            </div>
          </div>

          {/* Payment & Subscription Card */}
          <div
            onClick={() => setActiveTab('payment')}
            className={`group relative p-6 rounded-xl cursor-pointer transition-all duration-200 ${
              activeTab === 'payment'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 transition-all duration-200 ${
                activeTab === 'payment'
                  ? 'bg-white/20 text-white'
                  : 'bg-green-50 text-green-600 group-hover:bg-green-100'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Payment & Subscription</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'payment' ? 'text-blue-100' : 'text-gray-600'}`}>
                View and manage your subscription plan, payment methods and earnings.
              </p>
            </div>
          </div>

          {/* Feedback and Support Card */}
          <div
            onClick={() => setActiveTab('support')}
            className={`group relative p-6 rounded-xl cursor-pointer transition-all duration-200 ${
              activeTab === 'support'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 transition-all duration-200 ${
                activeTab === 'support'
                  ? 'bg-white/20 text-white'
                  : 'bg-purple-50 text-purple-600 group-hover:bg-purple-100'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Feedback and Support</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'support' ? 'text-blue-100' : 'text-gray-600'}`}>
                Report issues, contact support and access helpful resources.
              </p>
            </div>
          </div>
        </div>

        {/* Account Settings Section */}
        {activeTab === 'account' && (
          <div className="animate-fadeIn">
            <div className="flex items-center gap-3 mb-10">
              <h2 className="text-2xl font-bold text-gray-900">Account Settings</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Account Details */}
              <div className="lg:col-span-2 space-y-8">
                {/* Email Update */}
                <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-6">
                    <label className="text-lg font-semibold text-gray-800">Email id*</label>
                    <button className="bg-blue-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm hover:shadow-md">
                      Update
                    </button>
                  </div>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full px-4 py-3.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white"
                  />
                </div>

                {/* Current Password */}
                <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-6">
                    <label className="text-lg font-semibold text-gray-800">Current Password*</label>
                    <button className="bg-blue-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm hover:shadow-md">
                      Update
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.current ? "text" : "password"}
                      defaultValue="••••••••••••••••••"
                      className="w-full px-4 py-3.5 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('current')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1"
                    >
                      {showPassword.current ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* New Password */}
                <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200">
                  <div className="mb-6">
                    <label className="text-lg font-semibold text-gray-800">New Password</label>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.new ? "text" : "password"}
                      placeholder="••••••••••••••••••"
                      className="w-full px-4 py-3.5 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('new')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1"
                    >
                      {showPassword.new ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* Confirm New Password */}
                <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200">
                  <div className="flex items-center justify-between mb-6">
                    <label className="text-lg font-semibold text-gray-800">Confirm New Password</label>
                    <button className="bg-green-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-green-700 transition-colors shadow-sm hover:shadow-md">
                      Save
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.confirm ? "text" : "password"}
                      placeholder="••••••••••••••••••"
                      className="w-full px-4 py-3.5 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('confirm')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1"
                    >
                      {showPassword.confirm ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Right Column - Delete Account */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl p-8 shadow-lg border border-red-100 hover:shadow-xl transition-shadow duration-200">
                  <div className="mb-8">
                    <h3 className="text-xl font-bold text-red-700 mb-2">Delete Account</h3>
                    <p className="text-sm text-gray-600">Permanently remove your account</p>
                  </div>

                  <div className="space-y-6 mb-8">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Email id*</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-4 py-3.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all bg-gray-50 focus:bg-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Current Password*</label>
                      <div className="relative">
                        <input
                          type={showPassword.delete ? "text" : "password"}
                          placeholder="••••••••••••••••••"
                          className="w-full px-4 py-3.5 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all bg-gray-50 focus:bg-white"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('delete')}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1"
                        >
                          {showPassword.delete ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mb-8 p-5 bg-red-50 border-l-4 border-red-400 rounded-lg">
                    <div className="flex items-start gap-3">
                      <FaShieldAlt className="text-red-500 text-sm mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-red-700 font-medium leading-relaxed">
                        This Action Cannot Be Undone. All Your Data Will Be Permanently Deleted.
                      </p>
                    </div>
                  </div>

                  <button className="w-full bg-red-600 text-white py-3.5 rounded-lg font-semibold hover:bg-red-700 transition-colors shadow-sm hover:shadow-md">
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other Tabs Content */}
        {activeTab === 'notifications' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-12 shadow-lg border border-gray-100">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Notification Settings
                </h2>
                <p className="text-gray-600 text-lg max-w-lg mx-auto mb-8 leading-relaxed">
                  Customize your notification preferences and stay updated with what matters most to you.
                </p>
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-50 text-amber-700 rounded-lg font-medium">
                  <FaShieldAlt className="w-4 h-4" />
                  Coming Soon...
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-12 shadow-lg border border-gray-100">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Payment & Subscription
                </h2>
                <p className="text-gray-600 text-lg max-w-lg mx-auto mb-8 leading-relaxed">
                  Manage your subscription plans, payment methods, and billing information securely.
                </p>
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg font-medium">
                  <FaShieldAlt className="w-4 h-4" />
                  Coming Soon...
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'support' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-12 shadow-lg border border-gray-100">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Feedback and Support
                </h2>
                <p className="text-gray-600 text-lg max-w-lg mx-auto mb-8 leading-relaxed">
                  Get help, report issues, and share your feedback to help us improve your experience.
                </p>
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-50 text-purple-700 rounded-lg font-medium">
                  <FaShieldAlt className="w-4 h-4" />
                  Coming Soon...
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.8;
          }
        }

        .animate-pulse {
          animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
}

export default SettingsPage;
