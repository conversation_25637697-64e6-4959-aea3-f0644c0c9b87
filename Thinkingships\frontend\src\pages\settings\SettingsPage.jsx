import { useState } from 'react';
import { FaShieldAlt } from 'react-icons/fa';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
    delete: false
  });

  const togglePasswordVisibility = (field) => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 py-8">
        {/* Compact Settings Header */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Account Settings
          </h1>
          <p className="text-gray-600 max-w-xl mx-auto">
            Manage your account preferences, security settings, and personal information with ease
          </p>
        </div>

        {/* Compact Settings Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {/* Account Card */}
          <div
            onClick={() => setActiveTab('account')}
            className={`group relative p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
              activeTab === 'account'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 transition-all duration-300 ${
                activeTab === 'account'
                  ? 'bg-white/20 text-white'
                  : 'bg-blue-50 text-blue-600'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-bold mb-3">Account</h3>
              <p className={`text-sm ${activeTab === 'account' ? 'text-blue-100' : 'text-gray-600'}`}>
                Manage your account details, change your email or password and delete your account if needed.
              </p>
            </div>
          </div>

          {/* Notifications Card */}
          <div
            onClick={() => setActiveTab('notifications')}
            className={`group relative p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
              activeTab === 'notifications'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 transition-all duration-300 ${
                activeTab === 'notifications'
                  ? 'bg-white/20 text-white'
                  : 'bg-amber-50 text-amber-600'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-bold mb-3">Notifications</h3>
              <p className={`text-sm ${activeTab === 'notifications' ? 'text-blue-100' : 'text-gray-600'}`}>
                Customize your notification preferences for messages, content engagement and other updates.
              </p>
            </div>
          </div>

          {/* Payment & Subscription Card */}
          <div
            onClick={() => setActiveTab('payment')}
            className={`group relative p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
              activeTab === 'payment'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 transition-all duration-300 ${
                activeTab === 'payment'
                  ? 'bg-white/20 text-white'
                  : 'bg-green-50 text-green-600'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-bold mb-3">Payment & Subscription</h3>
              <p className={`text-sm ${activeTab === 'payment' ? 'text-blue-100' : 'text-gray-600'}`}>
                View and manage your subscription plan, payment methods and earnings.
              </p>
            </div>
          </div>

          {/* Feedback and Support Card */}
          <div
            onClick={() => setActiveTab('support')}
            className={`group relative p-6 rounded-2xl cursor-pointer transition-all duration-300 ${
              activeTab === 'support'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-md'
            }`}
          >
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl mb-4 transition-all duration-300 ${
                activeTab === 'support'
                  ? 'bg-white/20 text-white'
                  : 'bg-purple-50 text-purple-600'
              }`}>
                <FaShieldAlt className="text-lg" />
              </div>
              <h3 className="text-lg font-bold mb-3">Feedback and Support</h3>
              <p className={`text-sm ${activeTab === 'support' ? 'text-blue-100' : 'text-gray-600'}`}>
                Report issues, contact support and access helpful resources.
              </p>
            </div>
          </div>
        </div>

        {/* Account Settings Section */}
        {activeTab === 'account' && (
          <div className="animate-fadeIn">
            <div className="flex items-center gap-3 mb-8">
              <h2 className="text-2xl font-bold text-gray-900">Account Settings</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Account Details */}
              <div className="lg:col-span-2 space-y-6">
                {/* Email Update */}
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Email id*</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                      Update
                    </button>
                  </div>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                  />
                </div>

                {/* Current Password */}
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Current Password*</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                      Update
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.current ? "text" : "password"}
                      defaultValue="••••••••••••••••••"
                      className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('current')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.current ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* New Password */}
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="mb-4">
                    <label className="text-lg font-semibold text-gray-800">New Password</label>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.new ? "text" : "password"}
                      placeholder="••••••••••••••••••"
                      className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('new')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.new ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* Confirm New Password */}
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <label className="text-lg font-semibold text-gray-800">Confirm New Password</label>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                      Save
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.confirm ? "text" : "password"}
                      placeholder="••••••••••••••••••"
                      className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('confirm')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.confirm ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Right Column - Delete Account */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                  <div className="mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Delete Account</h3>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Email id*</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Current Password*</label>
                      <div className="relative">
                        <input
                          type={showPassword.delete ? "text" : "password"}
                          placeholder="••••••••••••••••••"
                          className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('delete')}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                          {showPassword.delete ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700 font-medium">
                      This Action Cannot Be Undone. All Your Data Will Be Permanently Deleted.
                    </p>
                  </div>

                  <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Confirm
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other Tabs Content */}
        {activeTab === 'notifications' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-amber-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <FaShieldAlt className="text-white text-2xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Notification Settings
                </h2>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  Customize your notification preferences and stay updated with what matters most to you.
                </p>
                <div className="text-amber-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <FaShieldAlt className="text-white text-2xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Payment & Subscription
                </h2>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  Manage your subscription plans, payment methods, and billing information securely.
                </p>
                <div className="text-green-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'support' && (
          <div className="animate-fadeIn">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <FaShieldAlt className="text-white text-2xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Feedback and Support
                </h2>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  Get help, report issues, and share your feedback to help us improve your experience.
                </p>
                <div className="text-purple-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.8;
          }
        }

        .animate-pulse {
          animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
}

export default SettingsPage;
