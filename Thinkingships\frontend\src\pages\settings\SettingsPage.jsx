import { useState } from 'react';
import { FaShieldAlt } from 'react-icons/fa';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
    delete: false
  });

  const togglePasswordVisibility = (field) => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Enhanced Settings Header */}
        <div className="mb-12 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl mb-6 shadow-lg">
            <FaShieldAlt className="text-white text-2xl" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent mb-4">
            Account Settings
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Manage your account preferences, security settings, and personal information with ease
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4 rounded-full"></div>
        </div>

        {/* Enhanced Settings Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {/* Account Card */}
          <div
            onClick={() => setActiveTab('account')}
            className={`group relative p-8 rounded-3xl cursor-pointer transition-all duration-500 transform hover:scale-105 ${
              activeTab === 'account'
                ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white shadow-2xl shadow-blue-500/25'
                : 'bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-200/50 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10'
            }`}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-14 h-14 rounded-2xl mb-6 transition-all duration-300 ${
                activeTab === 'account'
                  ? 'bg-white/20 text-white'
                  : 'bg-blue-50 text-blue-600 group-hover:bg-blue-100'
              }`}>
                <FaShieldAlt className="text-xl" />
              </div>
              <h3 className="text-xl font-bold mb-4">Account</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'account' ? 'text-blue-100' : 'text-gray-600'}`}>
                Manage your account details, change your email or password and delete your account if needed.
              </p>
            </div>
          </div>

          {/* Notifications Card */}
          <div
            onClick={() => setActiveTab('notifications')}
            className={`group relative p-8 rounded-3xl cursor-pointer transition-all duration-500 transform hover:scale-105 ${
              activeTab === 'notifications'
                ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white shadow-2xl shadow-blue-500/25'
                : 'bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-200/50 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10'
            }`}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-14 h-14 rounded-2xl mb-6 transition-all duration-300 ${
                activeTab === 'notifications'
                  ? 'bg-white/20 text-white'
                  : 'bg-amber-50 text-amber-600 group-hover:bg-amber-100'
              }`}>
                <FaShieldAlt className="text-xl" />
              </div>
              <h3 className="text-xl font-bold mb-4">Notifications</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'notifications' ? 'text-blue-100' : 'text-gray-600'}`}>
                Customize your notification preferences for messages, content engagement and other updates.
              </p>
            </div>
          </div>

          {/* Payment & Subscription Card */}
          <div
            onClick={() => setActiveTab('payment')}
            className={`group relative p-8 rounded-3xl cursor-pointer transition-all duration-500 transform hover:scale-105 ${
              activeTab === 'payment'
                ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white shadow-2xl shadow-blue-500/25'
                : 'bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-200/50 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10'
            }`}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-14 h-14 rounded-2xl mb-6 transition-all duration-300 ${
                activeTab === 'payment'
                  ? 'bg-white/20 text-white'
                  : 'bg-green-50 text-green-600 group-hover:bg-green-100'
              }`}>
                <FaShieldAlt className="text-xl" />
              </div>
              <h3 className="text-xl font-bold mb-4">Payment & Subscription</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'payment' ? 'text-blue-100' : 'text-gray-600'}`}>
                View and manage your subscription plan, payment methods and earnings.
              </p>
            </div>
          </div>

          {/* Feedback and Support Card */}
          <div
            onClick={() => setActiveTab('support')}
            className={`group relative p-8 rounded-3xl cursor-pointer transition-all duration-500 transform hover:scale-105 ${
              activeTab === 'support'
                ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 text-white shadow-2xl shadow-blue-500/25'
                : 'bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-200/50 hover:border-blue-300 hover:shadow-xl hover:shadow-blue-500/10'
            }`}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-14 h-14 rounded-2xl mb-6 transition-all duration-300 ${
                activeTab === 'support'
                  ? 'bg-white/20 text-white'
                  : 'bg-purple-50 text-purple-600 group-hover:bg-purple-100'
              }`}>
                <FaShieldAlt className="text-xl" />
              </div>
              <h3 className="text-xl font-bold mb-4">Feedback and Support</h3>
              <p className={`text-sm leading-relaxed ${activeTab === 'support' ? 'text-blue-100' : 'text-gray-600'}`}>
                Report issues, contact support and access helpful resources.
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Account Settings Section */}
        {activeTab === 'account' && (
          <div className="animate-fadeIn">
            <div className="flex items-center gap-4 mb-12">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <FaShieldAlt className="text-white text-lg" />
              </div>
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">Account Settings</h2>
                <p className="text-gray-600 mt-1">Manage your personal information and security preferences</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
              {/* Left Column - Account Details */}
              <div className="lg:col-span-2 space-y-8">
                {/* Email Update */}
                <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-500">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-50 rounded-xl flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                        <FaShieldAlt className="text-blue-600 text-sm" />
                      </div>
                      <label className="text-xl font-bold text-gray-800">Email Address*</label>
                    </div>
                    <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      Update
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full px-6 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 text-lg bg-gray-50/50"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                </div>

                {/* Current Password */}
                <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-500">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-amber-50 rounded-xl flex items-center justify-center group-hover:bg-amber-100 transition-colors">
                        <FaShieldAlt className="text-amber-600 text-sm" />
                      </div>
                      <label className="text-xl font-bold text-gray-800">Current Password*</label>
                    </div>
                    <button className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-3 rounded-xl font-semibold hover:from-amber-600 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      Update
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.current ? "text" : "password"}
                      defaultValue="••••••••••••••••••"
                      className="w-full px-6 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-amber-500/20 focus:border-amber-500 transition-all duration-300 text-lg bg-gray-50/50"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('current')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.current ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* New Password */}
                <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-500">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-green-50 rounded-xl flex items-center justify-center group-hover:bg-green-100 transition-colors">
                      <FaShieldAlt className="text-green-600 text-sm" />
                    </div>
                    <label className="text-xl font-bold text-gray-800">New Password</label>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.new ? "text" : "password"}
                      placeholder="Enter your new password"
                      className="w-full px-6 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-green-500/20 focus:border-green-500 transition-all duration-300 text-lg bg-gray-50/50"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('new')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.new ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* Confirm New Password */}
                <div className="group bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-200/50 hover:shadow-xl transition-all duration-500">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-50 rounded-xl flex items-center justify-center group-hover:bg-purple-100 transition-colors">
                        <FaShieldAlt className="text-purple-600 text-sm" />
                      </div>
                      <label className="text-xl font-bold text-gray-800">Confirm New Password</label>
                    </div>
                    <button className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                      Save Changes
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type={showPassword.confirm ? "text" : "password"}
                      placeholder="Confirm your new password"
                      className="w-full px-6 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-300 text-lg bg-gray-50/50"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('confirm')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword.confirm ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Right Column - Enhanced Delete Account */}
              <div className="lg:col-span-1">
                <div className="group bg-gradient-to-br from-red-50 via-white to-red-50/50 backdrop-blur-sm rounded-3xl p-8 shadow-lg border-2 border-red-200/50 hover:shadow-xl transition-all duration-500">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-red-700">Delete Account</h3>
                      <p className="text-red-600/80 text-sm">Permanently remove your account</p>
                    </div>
                  </div>

                  <div className="space-y-6 mb-8">
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-3">Email Address*</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-5 py-4 border-2 border-red-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-red-500/20 focus:border-red-500 transition-all duration-300 text-sm bg-white/80"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-3">Current Password*</label>
                      <div className="relative">
                        <input
                          type={showPassword.delete ? "text" : "password"}
                          placeholder="Enter your current password"
                          className="w-full px-5 py-4 pr-14 border-2 border-red-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-red-500/20 focus:border-red-500 transition-all duration-300 text-sm bg-white/80"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('delete')}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                          {showPassword.delete ? <FaShieldAlt className="w-4 h-4" /> : <FaShieldAlt className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mb-8 p-6 bg-gradient-to-r from-red-100 to-red-50 border-l-4 border-red-500 rounded-xl">
                    <div className="flex items-start gap-3">
                      <FaShieldAlt className="text-red-500 text-lg mt-1 flex-shrink-0" />
                      <div>
                        <h4 className="font-bold text-red-800 mb-2">Warning!</h4>
                        <p className="text-sm text-red-700 leading-relaxed">
                          This action cannot be undone. All your data, stories, and account information will be permanently deleted.
                        </p>
                      </div>
                    </div>
                  </div>

                  <button className="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-4 rounded-xl font-bold hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-500/20">
                    Confirm Deletion
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Other Tabs Content */}
        {activeTab === 'notifications' && (
          <div className="animate-fadeIn">
            <div className="bg-gradient-to-br from-amber-50 via-white to-amber-50/50 backdrop-blur-sm rounded-3xl p-12 shadow-xl border border-amber-200/50">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-amber-500 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-700 to-orange-700 bg-clip-text text-transparent mb-4">
                  Notification Settings
                </h2>
                <p className="text-gray-600 text-lg max-w-md mx-auto">
                  Customize your notification preferences and stay updated with what matters most to you.
                </p>
                <div className="mt-8 text-amber-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="animate-fadeIn">
            <div className="bg-gradient-to-br from-green-50 via-white to-green-50/50 backdrop-blur-sm rounded-3xl p-12 shadow-xl border border-green-200/50">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-green-700 to-emerald-700 bg-clip-text text-transparent mb-4">
                  Payment & Subscription
                </h2>
                <p className="text-gray-600 text-lg max-w-md mx-auto">
                  Manage your subscription plans, payment methods, and billing information securely.
                </p>
                <div className="mt-8 text-green-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'support' && (
          <div className="animate-fadeIn">
            <div className="bg-gradient-to-br from-purple-50 via-white to-purple-50/50 backdrop-blur-sm rounded-3xl p-12 shadow-xl border border-purple-200/50">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-700 to-indigo-700 bg-clip-text text-transparent mb-4">
                  Feedback and Support
                </h2>
                <p className="text-gray-600 text-lg max-w-md mx-auto">
                  Get help, report issues, and share your feedback to help us improve your experience.
                </p>
                <div className="mt-8 text-purple-600 font-medium">Coming Soon...</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.8;
          }
        }

        .animate-pulse {
          animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
}

export default SettingsPage;
